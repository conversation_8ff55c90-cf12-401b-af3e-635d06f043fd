-- Create database
CREATE DATABASE IF NOT EXISTS raymart_diner;

-- Use the database
USE raymart_diner;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create admin table
CREATE TABLE IF NOT EXISTS admin (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create menu_items table
CREATE TABLE IF NOT EXISTS menu_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    image VARCHAR(255),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    items TEXT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') DEFAULT 'cash',
    status ENUM('pending', 'preparing', 'completed', 'cancelled') DEFAULT 'pending',
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create order_items table
CREATE TABLE IF NOT EXISTS order_items (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    menu_item_id INT(11) NOT NULL,
    quantity INT(11) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (menu_item_id) REFERENCES menu_items(id) ON DELETE RESTRICT
);

-- Create tables table
CREATE TABLE IF NOT EXISTS tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    capacity INT(11) NOT NULL,
    is_reserved BOOLEAN DEFAULT FALSE
);

-- Create reservations table
CREATE TABLE IF NOT EXISTS reservations (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    date DATE NOT NULL,
    time TIME NOT NULL,
    guests INT(11) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create reservation_tables table (junction table for many-to-many relationship)
CREATE TABLE IF NOT EXISTS reservation_tables (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    reservation_id INT(11) NOT NULL,
    table_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE CASCADE,
    UNIQUE KEY unique_reservation_table (reservation_id, table_id)
);

-- Create payment_transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'credit_card', 'debit_card', 'gcash', 'paymaya') NOT NULL,
    transaction_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    transaction_reference VARCHAR(100),
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Create receipts table
CREATE TABLE IF NOT EXISTS receipts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    order_id INT(11) NOT NULL,
    file_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
);

-- Create email_logs table
CREATE TABLE IF NOT EXISTS email_logs (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11),
    type ENUM('order', 'reservation', 'admin_notification') NOT NULL,
    status ENUM('sent', 'failed') NOT NULL,
    recipient VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user
INSERT INTO admin (username, password, name, email) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample tables
INSERT INTO tables (capacity, is_reserved) VALUES
(2, FALSE),
(2, FALSE),
(2, FALSE),
(2, FALSE),
(2, FALSE),
(4, FALSE),
(4, FALSE),
(4, FALSE),
(6, FALSE),
(6, FALSE)
ON DUPLICATE KEY UPDATE capacity = capacity;

-- Insert sample menu items
INSERT INTO menu_items (name, category, price, description) VALUES
('Adobo', 'main', 150.00, 'Traditional Filipino braised pork and chicken in vinegar and soy sauce'),
('Lechon Kawali', 'main', 180.00, 'Crispy deep-fried pork belly served with liver sauce'),
('Sinigang na Baboy', 'main', 160.00, 'Sour pork soup with vegetables in tamarind broth'),
('Grilled Chicken', 'main', 140.00, 'Marinated grilled chicken with special sauce'),
('Fish Fillet', 'main', 170.00, 'Fresh fish fillet grilled to perfection'),
('Pancit Canton', 'noodles', 120.00, 'Stir-fried wheat noodles with vegetables and meat'),
('Pancit Bihon', 'noodles', 110.00, 'Stir-fried rice noodles with vegetables'),
('Garlic Rice', 'rice', 50.00, 'Fragrant garlic fried rice'),
('Plain Rice', 'rice', 30.00, 'Steamed white rice'),
('Halo-Halo', 'dessert', 80.00, 'Traditional Filipino shaved ice dessert with mixed ingredients'),
('Leche Flan', 'dessert', 60.00, 'Creamy caramel custard dessert'),
('Fresh Lumpia', 'appetizer', 90.00, 'Fresh spring rolls with vegetables and peanut sauce'),
('Lumpia Shanghai', 'appetizer', 100.00, 'Crispy fried spring rolls with ground pork'),
('Coke', 'beverage', 35.00, 'Coca-Cola soft drink'),
('Sprite', 'beverage', 35.00, 'Lemon-lime soft drink'),
('Iced Tea', 'beverage', 40.00, 'Refreshing iced tea'),
('Fresh Buko Juice', 'beverage', 45.00, 'Fresh coconut water')
ON DUPLICATE KEY UPDATE name = name;
