<?php
// Handle reservation form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_reservation'])) {
    // Check if user is logged in
    if (!isLoggedIn()) {
        $_SESSION['message'] = 'Please login to make a reservation!';
        $_SESSION['message_type'] = 'danger';
        redirect('index.php?page=login');
    }

    // Get form data
    $date = sanitize($_POST['date']);
    $time = sanitize($_POST['time']);
    $guests = (int)$_POST['guests'];

    // Make sure date is in YYYY-MM-DD format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        // Try to convert from MM/DD/YYYY format
        $date_parts = explode('/', $date);
        if (count($date_parts) === 3) {
            $month = str_pad($date_parts[0], 2, '0', STR_PAD_LEFT);
            $day = str_pad($date_parts[1], 2, '0', STR_PAD_LEFT);
            $year = $date_parts[2];
            $date = "$year-$month-$day";
        }
    }

    // Make sure time is in HH:MM:SS format
    if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
        // Try to convert from 12-hour format (h:mm AM/PM)
        if (preg_match('/(\d{1,2}):(\d{2}) (AM|PM)/i', $time, $matches)) {
            $hours = (int)$matches[1];
            $minutes = $matches[2];
            $ampm = strtoupper($matches[3]);

            // Convert to 24-hour format
            if ($ampm === 'PM' && $hours < 12) {
                $hours += 12;
            } elseif ($ampm === 'AM' && $hours === 12) {
                $hours = 0;
            }

            $time = sprintf('%02d:%02d:00', $hours, $minutes);
        }
    }

    // Validate form data
    $errors = [];

    if (empty($date)) {
        $errors[] = 'Date is required!';
    } else {
        $current_date = date('Y-m-d');
        if ($date < $current_date) {
            $errors[] = 'Date cannot be in the past!';
        }
    }

    if (empty($time)) {
        $errors[] = 'Time is required!';
    } else {
        // Check if time is within operating hours (9:00 AM - 9:00 PM)
        $time_obj = new DateTime($time);
        $open_time = new DateTime('09:00:00');
        $close_time = new DateTime('21:00:00');

        if ($time_obj < $open_time || $time_obj > $close_time) {
            $errors[] = 'Reservation time must be between 9:00 AM and 9:00 PM!';
        }
    }

    if ($guests < 1) {
        $errors[] = 'Number of guests must be at least 1!';
    } else if ($guests > 20) {
        $errors[] = 'For parties larger than 20, please contact us directly!';
    }

    // Get available tables
    $available_tables = [];
    if (empty($errors)) {
        $available_tables = getAvailableTables($date, $time, $guests);

        if (empty($available_tables)) {
            $errors[] = 'Sorry, no tables are available for the selected date, time, and party size. Please try a different time or date.';
        }
    }

    // Get selected tables from form
    $selected_tables = [];
    if (isset($_POST['selected_tables']) && !empty($_POST['selected_tables'])) {
        try {
            $selected_tables = json_decode($_POST['selected_tables'], true);

            // Validate that only one table is selected
            if (count($selected_tables) > 1) {
                $errors[] = 'Please select only one table for your reservation.';
            }

            // Validate that selected tables are actually available
            if (!empty($selected_tables)) {
                $valid_table_ids = array_column($available_tables, 'id');

                foreach ($selected_tables as $table_id) {
                    if (!in_array((int)$table_id, $valid_table_ids)) {
                        $errors[] = 'The selected table is no longer available. Please try again.';
                        break;
                    }
                }
            }
        } catch (Exception $e) {
            $errors[] = 'Invalid table selection. Please try again.';
        }
    }

    // If no errors, save reservation
    if (empty($errors)) {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Insert reservation
            $stmt = $conn->prepare("INSERT INTO reservations (user_id, date, time, guests, status) VALUES (?, ?, ?, ?, 'pending')");
            $stmt->bind_param("issi", $_SESSION['user_id'], $date, $time, $guests);
            $stmt->execute();
            $reservation_id = $stmt->insert_id;

            // Use selected tables if provided, otherwise find optimal tables
            $tables_to_assign = [];
            if (!empty($selected_tables)) {
                $tables_to_assign = $selected_tables;
            } else {
                $tables_to_assign = findOptimalTables($available_tables, $guests);
            }

            // Assign tables to reservation
            foreach ($tables_to_assign as $table_id) {
                $stmt = $conn->prepare("INSERT INTO reservation_tables (reservation_id, table_id) VALUES (?, ?)");
                $stmt->bind_param("ii", $reservation_id, $table_id);
                $stmt->execute();
            }

            // Get assigned tables for email
            $assigned_tables = getTablesForReservation($reservation_id);
            $table_info = '';
            if (!empty($assigned_tables)) {
                $table = $assigned_tables[0]; // Since we only allow one table now
                $table_info = "Table #" . $table['id'] . " (Capacity: " . $table['capacity'] . ")";
            }

            // Get user email
            $user = getUserById($_SESSION['user_id']);

            // Include admin notifications
            require_once __DIR__ . '/../includes/admin_notifications.php';

            // Send email notification to customer
            $subject = 'Reservation Confirmation - ' . SITE_NAME;
            $message = "Dear " . $user['name'] . ",\n\n";
            $message .= "Thank you for your reservation at " . SITE_NAME . "!\n\n";
            $message .= "Reservation ID: " . $reservation_id . "\n";
            $message .= "Date: " . formatDate($date) . "\n";
            $message .= "Time: " . formatTime($time) . "\n";
            $message .= "Number of Guests: " . $guests . "\n";
            $message .= "Assigned Table: " . $table_info . "\n\n";
            $message .= "Your reservation is currently pending confirmation. We will notify you once it's confirmed.\n\n";
            $message .= "Thank you for choosing " . SITE_NAME . "!\n";

            // Send email to customer
            sendEmail($user['email'], $subject, $message, $_SESSION['user_id'], 'reservation');

            // Send notification to admin about the new reservation
            $reservation_details = [
                'date' => $date,
                'time' => $time,
                'guests' => $guests,
                'tables' => $assigned_tables
            ];
            notifyAdminNewReservation($reservation_id, $reservation_details, $user);

            // Commit transaction
            $conn->commit();

            // Set success message
            $_SESSION['message'] = 'Reservation submitted successfully! Check your email for confirmation.';
            $_SESSION['message_type'] = 'success';

            // Redirect to profile page
            redirect('index.php?page=profile');
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();

            // Set error message
            $_SESSION['message'] = 'Failed to submit reservation. Please try again. Error: ' . $e->getMessage();
            $_SESSION['message_type'] = 'danger';
        }
    } else {
        // Set error message
        $_SESSION['message'] = implode('<br>', $errors);
        $_SESSION['message_type'] = 'danger';
    }
}
?>

<div class="container">
    <h1 class="mb-4">Make a Reservation</h1>

    <div class="row">
        <div class="col-lg-6">
            <div class="reservation-form">
                <form method="post" action="<?php echo SITE_URL; ?>/index.php?page=reservation">
                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        <div class="input-group date-input-group">
                            <input type="text" class="form-control" id="reservation-date" name="date" required placeholder="mm/dd/yyyy" autocomplete="off" readonly>
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                        <small class="text-muted">Please select a date within the next 30 days</small>
                        <!-- Hidden input for server-side processing -->
                        <input type="hidden" id="formatted-date" name="formatted_date">
                    </div>

                    <div class="mb-3">
                        <label for="time" class="form-label">Time</label>
                        <div class="input-group time-input-group">
                            <input type="text" class="form-control" id="reservation-time" name="time" required placeholder="hh:mm AM/PM" autocomplete="off" readonly>
                            <span class="input-group-text time-picker-icon"><i class="fas fa-clock"></i></span>
                        </div>
                        <small class="text-muted">Our operating hours are from 9:00 AM to 9:00 PM. Tables are available in 30-minute time slots.</small>
                        <!-- Hidden input for server-side processing -->
                        <input type="hidden" id="formatted-time" name="formatted_time">
                        <!-- Time selection dropdown (custom implementation) -->
                        <div id="time-dropdown" class="time-dropdown-menu">
                            <div class="time-dropdown-header">Select Time</div>
                            <div class="time-dropdown-content">
                                <!-- Time slots will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="guests" class="form-label">Number of Guests</label>
                        <div class="guest-selector mb-2">
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-primary" id="decrease-guests">
                                    <i class="fas fa-arrow-down"></i>
                                </button>
                                <div class="form-control text-center" id="guests-display">2</div>
                                <button type="button" class="btn btn-outline-primary" id="increase-guests">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                                <input type="hidden" id="guests" name="guests" value="2">
                            </div>
                        </div>
                        <small class="text-muted">For parties larger than 20, please contact us directly</small>
                    </div>

                    <!-- Table Selection Section -->
                    <div class="mb-3">
                        <label class="form-label">Table Selection</label>
                        <div id="table-selection-container" class="mb-3">
                            <div class="alert alert-info">
                                Please select a date, time, and number of guests to see available tables.
                            </div>
                        </div>
                        <div id="table-selection-loading" class="text-center" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Loading available tables...</p>
                        </div>
                        <!-- Hidden input for selected tables -->
                        <input type="hidden" id="selected-tables" name="selected_tables">
                    </div>

                    <div class="mb-3">
                        <label for="special_requests" class="form-label">Special Requests (Optional)</label>
                        <textarea class="form-control" id="special_requests" name="special_requests" rows="3"></textarea>
                    </div>

                    <?php if (!isLoggedIn()): ?>
                        <div class="alert alert-info">
                            Please <a href="<?php echo SITE_URL; ?>/index.php?page=login">login</a> to make a reservation.
                        </div>
                        <button type="submit" class="btn btn-primary btn-lg" name="submit_reservation" disabled>Submit Reservation</button>
                    <?php else: ?>
                        <button type="submit" class="btn btn-primary btn-lg" name="submit_reservation">Submit Reservation</button>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <div class="col-lg-6 mt-4 mt-lg-0">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">Reservation Information</h5>
                    <hr>

                    <h6>Why Make a Reservation?</h6>
                    <p>Making a reservation ensures that you have a table waiting for you when you arrive at Raymart's Diner. It helps us prepare for your visit and provide you with the best possible dining experience.</p>

                    <h6>Reservation Policy</h6>
                    <ul>
                        <li>Reservations can be made up to 30 days in advance.</li>
                        <li>Tables are available in 30-minute time slots.</li>
                        <li>We hold reservations for 15 minutes past the reserved time.</li>
                        <li>For parties larger than 20, please contact us directly.</li>
                        <li>Cancellations should be made at least 24 hours in advance.</li>
                    </ul>

                    <h6>Contact Information</h6>
                    <p>If you have any questions or need to make changes to your reservation, please contact us:</p>
                    <p><i class="fas fa-phone"></i> +63 ************</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Add custom calendar CSS
    const customCalendarCss = document.createElement('link');
    customCalendarCss.rel = 'stylesheet';
    customCalendarCss.href = '<?php echo SITE_URL; ?>/assets/css/custom-calendar.css';
    document.head.appendChild(customCalendarCss);

    // Override jQuery UI datepicker icons with Font Awesome
    $.datepicker._updateDatepicker_original = $.datepicker._updateDatepicker;
    $.datepicker._updateDatepicker = function(inst) {
        $.datepicker._updateDatepicker_original(inst);
        var afterShow = this._get(inst, 'afterShow');
        if (afterShow) {
            afterShow.apply((inst.input ? inst.input[0] : null));
        }
        // Replace the navigation arrows with custom icons
        $('.ui-datepicker-prev span').html('<i class="fas fa-chevron-up"></i>');
        $('.ui-datepicker-next span').html('<i class="fas fa-chevron-down"></i>');
    };

    $(document).ready(function() {
        // Initialize datepicker with mm/dd/yyyy format
        $("#reservation-date").datepicker({
            dateFormat: 'mm/dd/yy',
            changeMonth: true,
            changeYear: true,
            yearRange: 'c:c+1',
            minDate: 0,
            maxDate: '+30d',
            showOtherMonths: true,
            selectOtherMonths: true,
            showAnim: "fadeIn",
            showButtonPanel: true,
            closeText: "Clear",
            currentText: "Today",
            beforeShow: function(input, inst) {
                // Ensure the datepicker is always visible
                setTimeout(function() {
                    inst.dpDiv.css({
                        top: $(input).offset().top + $(input).outerHeight(),
                        left: $(input).offset().left
                    });

                    // Add custom footer with Clear and Today buttons if not already added
                    if (!inst.dpDiv.find('.ui-datepicker-footer').length) {
                        const footer = $('<div class="ui-datepicker-footer"></div>');
                        const clearBtn = $('<button type="button" class="ui-datepicker-clear">Clear</button>');
                        const todayBtn = $('<button type="button" class="ui-datepicker-today">Today</button>');

                        clearBtn.on('click', function() {
                            $("#reservation-date").datepicker('setDate', null);
                            $("#formatted-date").val('');
                            $("#reservation-date").datepicker('hide');
                        });

                        todayBtn.on('click', function() {
                            const today = new Date();
                            $("#reservation-date").datepicker('setDate', today);
                            const month = (today.getMonth() + 1).toString().padStart(2, '0');
                            const day = today.getDate().toString().padStart(2, '0');
                            const year = today.getFullYear();
                            $("#formatted-date").val(`${year}-${month}-${day}`);
                            $("#reservation-date").datepicker('hide');
                        });

                        footer.append(clearBtn).append(todayBtn);
                        inst.dpDiv.append(footer);
                    }
                }, 0);
            },
            onSelect: function(dateText, inst) {
                // When a date is selected, format it for server-side processing
                try {
                    const dateParts = dateText.split('/');
                    if (dateParts.length === 3) {
                        const month = dateParts[0].padStart(2, '0');
                        const day = dateParts[1].padStart(2, '0');
                        const year = dateParts[2];

                        // Set the hidden input with the formatted date (yyyy-mm-dd)
                        const formattedDate = `${year}-${month}-${day}`;
                        $("#formatted-date").val(formattedDate);

                        // Reset time selection when date changes
                        $("#reservation-time").val("");
                        $("#formatted-time").val("");

                        // If the time dropdown is visible, refresh it to show available times for the new date
                        if ($timeDropdown.is(":visible")) {
                            populateTimeDropdown();
                        }
                    }
                } catch (e) {
                    console.error("Error formatting date:", e);
                }
            }
        });

        // Add calendar icon click handler
        $(".date-input-group .input-group-text").on("click", function() {
            $("#reservation-date").datepicker("show");
        });

        // Format the date before form submission
        $("form").on("submit", function(e) {
            const dateInput = $("#reservation-date");
            const formattedDateInput = $("#formatted-date");

            if (dateInput.val() && !formattedDateInput.val()) {
                try {
                    // Convert mm/dd/yyyy to yyyy-mm-dd for server processing
                    const dateParts = dateInput.val().split('/');
                    if (dateParts.length === 3) {
                        const month = dateParts[0].padStart(2, '0');
                        const day = dateParts[1].padStart(2, '0');
                        const year = dateParts[2];

                        // Set the hidden input with the formatted date
                        const formattedDate = `${year}-${month}-${day}`;
                        formattedDateInput.val(formattedDate);
                    } else {
                        // If date format is invalid, prevent form submission
                        e.preventDefault();
                        alert("Please enter a valid date in mm/dd/yyyy format");
                        dateInput.focus();
                    }
                } catch (e) {
                    console.error("Error formatting date:", e);
                    e.preventDefault();
                }
            }

            // Use the formatted date for submission
            if (formattedDateInput.val()) {
                dateInput.attr('name', '_display_date'); // Rename original field so it's not submitted
                formattedDateInput.attr('name', 'date'); // Use the formatted date as the 'date' parameter
            }
        });

        // Custom time picker implementation
        const $timeInput = $("#reservation-time");
        const $timeDropdown = $("#time-dropdown");
        const $timeDropdownContent = $timeDropdown.find(".time-dropdown-content");
        const $formattedTimeInput = $("#formatted-time");

        // Generate time slots from 9:00 AM to 9:00 PM in 30-minute intervals
        function generateTimeSlots() {
            const timeSlots = [];
            for (let hour = 9; hour <= 21; hour++) {
                for (let minute = 0; minute < 60; minute += 30) {
                    // Skip 9:30 PM (last slot is 9:00 PM)
                    if (hour === 21 && minute === 30) continue;

                    const h = hour % 12 || 12; // Convert to 12-hour format
                    const ampm = hour < 12 ? 'AM' : 'PM';
                    const m = minute.toString().padStart(2, '0');

                    timeSlots.push({
                        display: `${h}:${m} ${ampm}`,
                        value: `${hour.toString().padStart(2, '0')}:${m}:00`
                    });
                }
            }
            return timeSlots;
        }

        // Get reserved time slots for a specific date and party size
        function getReservedTimeSlots(date) {
            // Get the current guest count
            const guestCount = $("#guests").val();

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '<?php echo SITE_URL; ?>/get_reserved_times.php',
                    type: 'GET',
                    data: {
                        date: date,
                        guests: guestCount
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.error) {
                            reject(response.error);
                        } else {
                            resolve(response.reserved_times || []);
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        }

        // Populate the time dropdown
        async function populateTimeDropdown() {
            try {
                const timeSlots = generateTimeSlots();
                $timeDropdownContent.empty();

                // Get the selected date
                const selectedDate = $("#formatted-date").val();

                // Get reserved time slots for the selected date
                let reservedTimes = [];
                if (selectedDate) {
                    try {
                        reservedTimes = await getReservedTimeSlots(selectedDate);
                    } catch (error) {
                        console.error("Error fetching reserved times:", error);
                    }
                }

                // Add header to explain 30-minute time slots
                const $timeHeader = $('<div class="time-slot-info"></div>')
                    .html('<small>Each slot is for a 30-minute reservation</small>');
                $timeDropdownContent.append($timeHeader);

                // Filter out reserved time slots
                timeSlots.forEach(slot => {
                    const isReserved = reservedTimes.includes(slot.value);

                    // Create time option element
                    const $timeOption = $('<div class="time-option"></div>')
                        .text(slot.display)
                        .data('value', slot.value);

                    // Add reserved class if the time slot is reserved
                    if (isReserved) {
                        $timeOption.addClass('reserved');
                        $timeOption.append(' <span class="reserved-badge">Reserved</span>');
                    } else {
                        // Only add click handler for available time slots
                        $timeOption.on('click', function() {
                            // Update the display and hidden inputs
                            $timeInput.val(slot.display);
                            $formattedTimeInput.val(slot.value);

                            // Update selected state
                            $timeDropdownContent.find('.time-option').removeClass('selected');
                            $(this).addClass('selected');

                            // Hide the dropdown
                            $timeDropdown.hide();

                            // Automatically update table selection when time is selected
                            updateTableSelection();
                        });
                    }

                    // Mark as selected if it matches the current value
                    if ($timeInput.val() === slot.display) {
                        $timeOption.addClass('selected');
                    }

                    $timeDropdownContent.append($timeOption);
                });
            } catch (error) {
                console.error("Error populating time dropdown:", error);
            }
        }

        // Show the time dropdown
        function showTimeDropdown() {
            // Position the dropdown correctly
            const inputGroup = $(".time-input-group");
            const inputHeight = inputGroup.outerHeight();

            $timeDropdown.css({
                top: inputHeight,
                width: inputGroup.width()
            }).show();

            // Populate with time slots
            populateTimeDropdown();
        }

        // Hide the time dropdown
        function hideTimeDropdown() {
            $timeDropdown.hide();
        }

        // Add click handler specifically for the clock icon
        $(".time-input-group .time-picker-icon").on("click", function(e) {
            e.preventDefault();
            e.stopPropagation();
            showTimeDropdown();
            return false; // Prevent event bubbling
        });

        // Make the time input field clickable too
        $timeInput.on("click", function(e) {
            e.preventDefault();
            e.stopPropagation();
            showTimeDropdown();
            return false; // Prevent event bubbling
        });

        // Close dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.time-input-group').length &&
                !$(e.target).closest('.time-dropdown-menu').length) {
                hideTimeDropdown();
            }
        });

        // Format the time before form submission
        $("form").on("submit", function(e) {
            const timeInput = $("#reservation-time");
            const formattedTimeInput = $("#formatted-time");

            if (timeInput.val() && !formattedTimeInput.val()) {
                try {
                    // Try to convert the time format
                    const timeParts = timeInput.val().match(/(\d+):(\d+) (\w+)/);
                    if (timeParts) {
                        let hours = parseInt(timeParts[1]);
                        const minutes = timeParts[2];
                        const ampm = timeParts[3].toLowerCase();

                        // Convert to 24-hour format
                        if (ampm === 'pm' && hours < 12) hours += 12;
                        if (ampm === 'am' && hours === 12) hours = 0;

                        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes}:00`;
                        formattedTimeInput.val(formattedTime);
                    } else {
                        // If time format is invalid, prevent form submission
                        e.preventDefault();
                        alert("Please select a valid time");
                        timeInput.focus();
                    }
                } catch (e) {
                    console.error("Error formatting time:", e);
                    e.preventDefault();
                }
            }

            // Use the formatted time for submission
            if (formattedTimeInput.val()) {
                timeInput.attr('name', '_display_time'); // Rename original field so it's not submitted
                formattedTimeInput.attr('name', 'time'); // Use the formatted time as the 'time' parameter
            }
        });

        // Set current date as default (formatted as mm/dd/yyyy)
        const today = new Date();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const year = today.getFullYear();
        const formattedToday = `${month}/${day}/${year}`;

        // Set display date and formatted date
        $("#reservation-date").val(formattedToday);
        $("#formatted-date").val(`${year}-${month}-${day}`);

        // Set default time (3:00 PM)
        $("#reservation-time").val("3:00 PM");
        $("#formatted-time").val("15:00:00");

        // Automatically show table selection on page load with default values
        setTimeout(updateTableSelection, 500);

        // We don't need the change listener for guests anymore since we're using buttons
        // The click handler for guest buttons already calls updateTableSelection()

        // Function to update table selection
        function updateTableSelection() {
            const date = $("#formatted-date").val();
            const time = $("#formatted-time").val();
            const guests = $("#guests").val();

            if (!date || !time || !guests) {
                // If any required field is missing, show the info message
                $("#table-selection-container").html(`
                    <div class="alert alert-info">
                        Please select a date, time, and number of guests to see available tables.
                    </div>
                `);
                return;
            }

            // Show loading indicator
            $("#table-selection-container").hide();
            $("#table-selection-loading").show();

            // Fetch available tables
            $.ajax({
                url: '<?php echo SITE_URL; ?>/get_available_tables_client.php',
                type: 'GET',
                data: {
                    date: date,
                    time: time,
                    guests: guests
                },
                dataType: 'json',
                success: function(response) {
                    // Hide loading indicator
                    $("#table-selection-loading").hide();
                    $("#table-selection-container").show();

                    if (response.error) {
                        $("#table-selection-container").html(`
                            <div class="alert alert-danger">
                                ${response.error}
                            </div>
                        `);
                        return;
                    }

                    if (!response.available_tables || response.available_tables.length === 0) {
                        $("#table-selection-container").html(`
                            <div class="alert alert-warning">
                                No tables are available for the selected date and time. Please try a different time or date.
                            </div>
                        `);
                        return;
                    }

                    // Display available tables
                    let tableSelectionHtml = `
                        <div class="table-selection-info mb-3">
                            <p>Choose one table for your party of ${response.guests}:</p>
                            <small class="text-muted">You can only select one table per reservation.</small>
                        </div>
                        <div class="row">
                    `;

                    // Sort tables by capacity
                    const sortedTables = [...response.available_tables].sort((a, b) => a.capacity - b.capacity);

                    // Mark optimal tables
                    const optimalTableIds = response.optimal_tables || [];

                    // Generate table cards
                    sortedTables.forEach(table => {
                        const isOptimal = optimalTableIds.includes(table.id);
                        const optimalBadge = isOptimal ? '<span class="badge bg-success recommendation-badge">Recommended</span>' : '';

                        tableSelectionHtml += `
                            <div class="col-md-4 mb-3">
                                <div class="card table-card ${isOptimal ? 'border-success' : ''}" data-table-id="${table.id}">
                                    ${isOptimal ? '<div class="recommendation-badge-container text-center"><span class="badge bg-success recommendation-badge">Recommended</span></div>' : ''}
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input table-radio" type="radio" name="table_selection" value="${table.id}" id="table_${table.id}" ${isOptimal ? 'checked' : ''}>
                                            <label class="form-check-label w-100" for="table_${table.id}">
                                                <h5 class="mb-1">Table #${table.id}</h5>
                                                <p class="mb-0">Capacity: ${table.capacity} ${table.capacity > 1 ? 'persons' : 'person'}</p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    tableSelectionHtml += `</div>`;

                    // Update the container
                    $("#table-selection-container").html(tableSelectionHtml);

                    // Add click handler for table cards
                    $(".table-card").on("click", function() {
                        const tableId = $(this).data("table-id");
                        const radio = $(`#table_${tableId}`);

                        // Select this radio button
                        radio.prop("checked", true);

                        // Update selected tables
                        updateSelectedTables();
                    });

                    // Add change handler for radio buttons
                    $(".table-radio").on("change", function(e) {
                        e.stopPropagation(); // Prevent triggering the card click
                        updateSelectedTables();
                    });

                    // Initialize selected tables
                    updateSelectedTables();
                },
                error: function(xhr, status, error) {
                    // Hide loading indicator
                    $("#table-selection-loading").hide();
                    $("#table-selection-container").show();

                    $("#table-selection-container").html(`
                        <div class="alert alert-danger">
                            Error loading available tables: ${error}
                        </div>
                    `);
                }
            });
        }

        // Function to update the selected tables hidden input
        function updateSelectedTables() {
            const selectedTables = [];
            const selectedRadio = $(".table-radio:checked");

            if (selectedRadio.length > 0) {
                selectedTables.push(selectedRadio.val());
            }

            // Update hidden input
            $("#selected-tables").val(JSON.stringify(selectedTables));
        }

        // Add listeners for date and time changes to update table selection
        $("#reservation-date").on("change", function() {
            // Wait for the formatted date to be updated
            setTimeout(updateTableSelection, 100);
        });

        $timeInput.on("change", function() {
            // Wait for the formatted time to be updated
            setTimeout(updateTableSelection, 100);
        });

        // Add event handlers for guest arrow buttons
        $("#increase-guests").on("click", function() {
            let currentValue = parseInt($("#guests").val());
            if (currentValue < 20) {
                currentValue++;
                updateGuestCount(currentValue);
            }
        });

        $("#decrease-guests").on("click", function() {
            let currentValue = parseInt($("#guests").val());
            if (currentValue > 1) {
                currentValue--;
                updateGuestCount(currentValue);
            }
        });

        // Function to update guest count
        function updateGuestCount(value) {
            // Update hidden input and display
            $("#guests").val(value);
            $("#guests-display").text(value);

            // Update table selection
            updateTableSelection();
        }

        // Add custom styles for table selection and guest buttons
        $("<style>")
            .text(`
                .table-card {
                    cursor: pointer;
                    transition: all 0.2s ease;
                }
                .table-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                }
                .table-card .form-check-input {
                    cursor: pointer;
                }
                .table-card .table-radio:checked + label {
                    font-weight: bold;
                    color: #198754;
                }
                .table-card.border-success {
                    border-width: 2px;
                }

                /* Recommendation badge styles */
                .recommendation-badge-container {
                    padding: 8px 0;
                    background-color: rgba(25, 135, 84, 0.1);
                    border-top-left-radius: 10px;
                    border-top-right-radius: 10px;
                }

                .recommendation-badge {
                    font-size: 0.85rem;
                    padding: 5px 10px;
                    display: inline-block;
                }

                /* Guest selector styles */
                .guest-selector .input-group {
                    max-width: 200px;
                }

                #guests-display {
                    font-size: 1.2rem;
                    font-weight: bold;
                    background-color: #fff;
                    cursor: default;
                }

                /* Time slot info styles */
                .time-slot-info {
                    padding: 8px;
                    text-align: center;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                    color: #6c757d;
                }

                /* Make buttons more touch-friendly on mobile */
                @media (max-width: 768px) {
                    .guest-selector .btn {
                        height: 45px;
                        font-size: 1.1rem;
                    }

                    #guests-display {
                        font-size: 1.5rem;
                        padding-top: 8px;
                    }
                }
            `)
            .appendTo("head");
    });
</script>
