/* Main Styles for RAYMART'S DINER */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&family=Montserrat:wght@400;500;600;700&display=swap');

/* General Styles */
body {
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    background-color: #f8f9fa;
    padding-top: 70px; /* Add padding to prevent content from being hidden behind fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #212529;
}

a {
    color: #dc3545;
    text-decoration: none;
}

a:hover {
    color: #c82333;
    text-decoration: underline;
}

.btn-primary {
    background-color: #212529;
    border-color: #212529;
    box-shadow: 2px 2px 0 #800020;
    transition: all 0.3s ease;
    text-decoration: none !important; /* Ensure no underline */
}

.btn-primary:hover {
    background-color: #343a40;
    border-color: #212529;
    box-shadow: 3px 3px 0 #800020;
    transform: translate(-1px, -1px);
    text-decoration: none !important; /* Ensure no underline */
}

.btn-outline-primary {
    color: #212529;
    border-color: #212529;
    box-shadow: 1px 1px 0 #800020;
    transition: all 0.3s ease;
    text-decoration: none !important; /* Ensure no underline */
}

.btn-outline-primary:hover {
    background-color: #212529;
    border-color: #212529;
    color: #fff;
    box-shadow: 2px 2px 0 #800020;
    transform: translate(-1px, -1px);
    text-decoration: none !important; /* Ensure no underline */
}

/* Additional button styles */
.btn-outline-light {
    text-decoration: none !important; /* Ensure no underline */
}

.btn-outline-light:hover {
    text-decoration: none !important; /* Ensure no underline */
}

/* Ensure all buttons never show underlines */
.btn {
    text-decoration: none !important; /* Ensure no underline for all buttons */
}

.btn:hover, .btn:focus, .btn:active {
    text-decoration: none !important; /* Ensure no underline for all buttons in all states */
}

/* Navbar */
.navbar {
    z-index: 1030; /* Ensure navbar stays on top of other elements */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Add subtle shadow for elevation effect */
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    letter-spacing: 1px;
    color: #fff !important;
    padding: 8px 18px;
    background-color: #212529; /* Dark charcoal */
    font-weight: 700;
    border-radius: 3px;
    text-decoration: none !important; /* Ensure no underline */
    position: relative; /* Required for absolute positioning of pseudo-element */
}

/* Disable hover effects on the logo */
.navbar-brand:hover {
    color: #fff !important;
    text-decoration: none !important;
    background-color: #212529 !important; /* Keep the same background color on hover */
}

/* Style for the second R in RAYMART'S */
.colored-r {
    color: #a52a2a; /* Light maroon color */
}

/* Removed hover effect for underline */

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none !important; /* Ensure no underline */
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link.active {
    color: #fff;
    text-decoration: none !important; /* Ensure no underline */
}

/* Hero Section */
.hero {
    background-size: cover;
    background-position: center;
    color: white;
    text-align: center;
    padding: 100px 0;
    margin-bottom: 30px;
    position: relative;
    margin-top: -20px; /* Adjust for fixed navbar */
}

.hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 30px;
}

/* Menu Items */
.menu-item {
    margin-bottom: 30px;
    transition: transform 0.3s;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
    transform: translateY(-5px);
}

.menu-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.menu-item-content {
    padding: 15px;
    background-color: white;
}

.menu-item-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.menu-item-price {
    color: #dc3545;
    font-weight: 700;
    margin-bottom: 10px;
}

.menu-item-description {
    color: #6c757d;
    margin-bottom: 15px;
}

/* Category Pills */
.category-pills {
    margin-bottom: 30px;
}

.category-pill {
    cursor: pointer;
    padding: 8px 15px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 20px;
    background-color: #f8f9fa;
    color: #495057;
    transition: all 0.3s;
}

.category-pill:hover,
.category-pill.active {
    background-color: #dc3545;
    color: white;
}

/* Cart */
.cart-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.cart-item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
}

.cart-summary {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Cart Badge */
.cart-icon-container {
    position: relative;
    display: inline-block;
    margin-right: 5px;
}

.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    font-size: 0.7rem;
    padding: 0.25rem 0.4rem;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid #c82333;
}

/* Cart Notification */
.cart-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 9999;
    background-color: #28a745;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    animation: slideIn 0.5s ease-out, fadeOut 0.5s ease-out 4.5s forwards;
    max-width: 350px;
    opacity: 1;
    visibility: visible;
}

.cart-notification i {
    font-size: 24px;
    margin-right: 10px;
}

.cart-notification-content {
    flex: 1;
}

.cart-notification h5 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: bold;
}

.cart-notification p {
    margin: 0;
    font-size: 14px;
}

/* Top Cart Alert Notification */
.cart-alert-notification {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 80%;
    max-width: 500px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease-out;
}

.cart-alert-notification .close-btn {
    background: none;
    border: none;
    color: #0f5132;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -20px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        visibility: visible;
    }
    to {
        opacity: 0;
        visibility: hidden;
    }
}

/* Cart icon animation */
@keyframes cartBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.cart-icon-animate {
    animation: cartBounce 1s ease;
    color: #ffc107;
}

/* Flying item to cart animation */
.cart-item-fly {
    position: fixed;
    z-index: 9999;
    background-color: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.8s cubic-bezier(0.18, 0.89, 0.32, 1.28);
    opacity: 1;
    border: 2px solid #28a745;
    overflow: hidden;
}

.cart-item-fly i {
    color: #28a745;
    font-size: 20px;
}

.cart-item-fly.flying {
    transform: scale(0.3) rotate(360deg);
    opacity: 0.9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

/* Reservation Form */
.reservation-form {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Custom Date/Time Input Styling */
.date-input-group .input-group-text,
.time-input-group .input-group-text {
    cursor: pointer;
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}

.date-input-group .input-group-text:hover,
.time-input-group .input-group-text:hover {
    background-color: #e9ecef;
}

.date-input-group input,
.time-input-group input {
    text-align: center;
}

/* Time Picker Styling */
.ui-timepicker-wrapper {
    overflow-y: auto;
    max-height: 200px;
    width: 160px !important;
    background: #fff;
    border: 1px solid #ddd;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    outline: none;
    z-index: 9999 !important;
    margin: 0;
    border-radius: 8px;
    font-family: 'Arial', sans-serif;
    position: absolute !important;
}

.ui-timepicker-list {
    margin: 0;
    padding: 5px 0;
    list-style: none;
}

.ui-timepicker-list li {
    padding: 8px 12px;
    cursor: pointer;
    white-space: nowrap;
    color: #333;
    list-style: none;
    margin: 0;
    transition: background-color 0.2s;
    text-align: center;
    font-size: 14px;
}

.ui-timepicker-list li:hover {
    background: #e9ecef;
    color: #000;
}

.ui-timepicker-list li.ui-timepicker-selected,
.ui-timepicker-list li.ui-timepicker-selected:hover {
    background: #007bff;
    color: #fff;
    font-weight: bold;
}

/* Custom Time Dropdown */
.time-input-group {
    position: relative;
}

.time-picker-icon {
    cursor: pointer;
    z-index: 3;
}

.time-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 9999 !important;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    margin-top: 5px;
    display: none; /* Hidden by default */
}

.time-dropdown-header {
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    font-weight: bold;
    border-radius: 8px 8px 0 0;
}

.time-dropdown-content {
    padding: 10px 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
}

.time-option {
    padding: 8px 12px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.time-option:hover {
    background-color: #e9ecef;
}

.time-option.reserved {
    color: #999;
    cursor: not-allowed;
    background-color: #f8f9fa;
    position: relative;
}

.time-option.reserved:hover {
    background-color: #f8f9fa;
}

.reserved-badge {
    font-size: 0.75rem;
    color: #fff;
    background-color: #dc3545;
    padding: 0.15rem 0.4rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.time-option.selected {
    background-color: #007bff;
    color: white;
    font-weight: bold;
}

/* Custom Datepicker Styling */
.ui-datepicker {
    font-family: 'Arial', sans-serif;
    font-size: 14px;
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border: none;
    border-radius: 8px;
    z-index: 1060 !important; /* Ensure it appears above other elements */
}

.ui-datepicker .ui-datepicker-header {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px 5px 0 0;
    padding: 8px;
}

.ui-datepicker .ui-datepicker-title {
    font-weight: bold;
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    cursor: pointer;
}

.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
    background: rgba(255,255,255,0.4);
}

.ui-datepicker table {
    margin: 5px 0;
}

.ui-datepicker th {
    color: #555;
    font-weight: bold;
    padding: 7px;
}

.ui-datepicker td {
    padding: 3px;
}

.ui-datepicker td span,
.ui-datepicker td a {
    text-align: center;
    padding: 7px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.ui-datepicker td a.ui-state-default {
    background: #f8f9fa;
    border: none;
    color: #333;
}

.ui-datepicker td a.ui-state-default:hover {
    background: #e9ecef;
}

.ui-datepicker td a.ui-state-active {
    background: #007bff;
    color: white;
}

.ui-datepicker td a.ui-state-highlight {
    background: #f8f9fa;
    border: 1px solid #007bff;
}

/* Ensure Submit Reservation button has the same styling as View Our Menu */
.reservation-form .btn-primary {
    background-color: #212529 !important;
    border-color: #212529 !important;
    box-shadow: 2px 2px 0 #800020 !important;
    color: #fff !important;
}

.reservation-form .btn-primary:hover {
    background-color: #343a40 !important;
    border-color: #212529 !important;
    box-shadow: 3px 3px 0 #800020 !important;
    transform: translate(-1px, -1px) !important;
}

/* Profile */
.profile-card {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.order-history-item,
.reservation-history-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

/* Footer */
footer {
    margin-top: auto;
}

.social-icons a {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s;
}

.social-icons a:hover {
    background-color: #dc3545;
    transform: translateY(-3px);
}

/* Hide number input spinners */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero {
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 40px 0;
    }

    .hero h1 {
        font-size: 2rem;
    }
}
